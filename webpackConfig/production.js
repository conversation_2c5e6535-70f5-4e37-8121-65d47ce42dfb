const { merge } = require("webpack-merge");
const webpackBaseConfig = require("./base.js");
const externals = require("./externals.js");
// const version = require("../package.json").version;
const path = require("path");
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const ScriptExtHtmlWebpackPlugin =require('script-ext-html-webpack-plugin');

function resolve(dir) {
    return path.join(__dirname, dir);
}
//  定义如果选择第三方依赖单独打包
let cacheGroupsConfig;
if (!externals.target === "externals") {
    cacheGroupsConfig = externals.cacheGroups;
}
module.exports = merge(webpackBaseConfig, {
    // 指定生产代码文件夹名称为当前版本号
    // outputDir: "dist",
    productionSourceMap: false,
    configureWebpack: {
        silenceDeprecations: ['legacy-js-api'],
        output: {
            // 输出重构  打包编译后的 文件名称 [模块名称].[内容hash:8].js
            filename: "./static/js/[name].[contenthash:8].bundle.js",
            chunkFilename: "./static/js/[name].[contenthash:8].chunk.js"
        },
        module: {
            rules: [
                {
                    test: /\.(png|jpe?g|gif|bmp)(\?.*)?$/,
                    type: "asset",
                    parser: {
                        dataUrlCondition: {
                            maxSize: 4 * 1024 // 4kb
                        }
                    },
                    generator: {
                        filename: "static/img/[name].[contenthash:8][ext]"
                    }
                }
            ]
        },
        plugins: [
            new BundleAnalyzerPlugin({
                //  可以是`server`，`static`或`disabled`。
                //  在`server`模式下，分析器将启动HTTP服务器来显示软件包报告。
                //  在“静态”模式下，会生成带有报告的单个HTML文件。
                //  在`disabled`模式下，你可以使用这个插件来将`generateStatsFile`设置为`true`来生成Webpack Stats JSON文件。
                analyzerMode: 'disabled',
                //  将在“服务器”模式下使用的主机启动HTTP服务器。
                analyzerHost: '127.0.0.1',
                //  将在“服务器”模式下使用的端口启动HTTP服务器。
                analyzerPort: 8888,
                //  路径捆绑，将在`static`模式下生成的报告文件。
                //  相对于捆绑输出目录。
                reportFilename: 'report.html',
                //  模块大小默认显示在报告中。
                //  应该是`stat`，`parsed`或者`gzip`中的一个。
                //  有关更多信息，请参见“定义”一节。
                defaultSizes: 'parsed',
                //  在默认浏览器中自动打开报告
                openAnalyzer: false,
                //  如果为true，则Webpack Stats JSON文件将在bundle输出目录中生成
                generateStatsFile: false,
                //  如果`generateStatsFile`为`true`，将会生成Webpack Stats JSON文件的名字。
                //  相对于捆绑输出目录。
                statsFilename: 'stats.json',
                //  stats.toJson（）方法的选项。
                //  例如，您可以使用`source：false`选项排除统计文件中模块的来源。
                //  在这里查看更多选项：https：  //github.com/webpack/webpack/blob/webpack-1/lib/Stats.js#L21
                statsOptions: null,
                logLevel: 'info' // 日志级别。可以是'信息'，'警告'，'错误'或'沉默'。
            }),
            new ScriptExtHtmlWebpackPlugin({
                custom: {
                    test: /\.js$/, // adjust this regex based on your demand
                    attribute: 'nonce',
                    value: 'test'
                }
            }),
        ]
    },
    chainWebpack(config) {
        // vue 文件去空格
        config.module
            .rule("vue")
            .use("vue-loader")
            .loader("vue-loader")
            .tap((options) => {
                options.compilerOptions.preserveWhitespace = true;
                return options;
            })
            .end();
        // 代码压缩
        config.optimization.minimize = true;
        // 代码切割
        // config.optimization.splitChunks({
        //     // 标签页下册出现默认滚动条遮挡内容，所以先注释，问题待排查
        //     chunks: "all",
        //     minSize: 30000,
        //     maxSize: 2400,
        //     minChunks: 1,
        //     maxAsyncRequests: 5,
        //     maxInitialRequests: 3,
        //     automaticNameDelimiter: "~",
        //     cacheGroups: {
        //         // 将放置在node_modules下的第三方依赖打包并且切割
        //         libs: {
        //             name: "libs",
        //             test: /[\\/]node_modules[\\/]/,
        //             // reuseExistingChunk: true,
        //             chunks: "all"
        //         },
        //         // 如果没有externals中的target为false,则需要手动配置单独打包的第三方依赖
        //         ...cacheGroupsConfig
        //     }
        // });
        config.optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
                libs: {
                    name: 'chunk-libs',
                    test: /[\\/]node_modules[\\/]/,
                    priority: 10,
                    chunks: 'initial' // only package third parties that are initially dependent
                },
                elementUI: {
                    name: 'chunk-elementUI', // split elementUI into a single package
                    priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                    test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
                },
                commons: {
                    name: 'chunk-commons',
                    test: resolve('src/components'), // can customize your rules
                    minChunks: 3, //  minimum common number
                    priority: 5,
                    reuseExistingChunk: true
                }
            }
        });
        //
        config.optimization.runtimeChunk("single");
    }
});
